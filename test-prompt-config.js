// Teste rápido para verificar configuração do Prompt ID
const fs = require('fs');
const path = require('path');

// Ler o arquivo .env.local
const envPath = path.join(__dirname, '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');

console.log('🔍 Verificando configuração do Prompt ID...\n');

// Extrair variáveis relevantes
const lines = envContent.split('\n');
const config = {};

lines.forEach(line => {
  if (line.includes('OPENAI_') && line.includes('=')) {
    const [key, value] = line.split('=');
    config[key.trim()] = value.trim();
  }
});

console.log('📋 Configuração encontrada:');
console.log('- OPENAI_API_KEY:', config.OPENAI_API_KEY ? '✅ Configurado' : '❌ Não encontrado');
console.log('- OPENAI_PROMPT_ID_CONTENT_GENERATION:', config.OPENAI_PROMPT_ID_CONTENT_GENERATION ? '✅ Configurado' : '❌ Não encontrado');
console.log('- OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT:', config.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT ? '✅ Configurado' : '❌ Não encontrado');
console.log('- OPENAI_PROMPT_ID_KEYWORD_SUGGESTION:', config.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION ? '✅ Configurado' : '❌ Não encontrado');

if (config.OPENAI_PROMPT_ID_CONTENT_GENERATION) {
  console.log('\n🔒 Prompt ID de geração de conteúdo:', config.OPENAI_PROMPT_ID_CONTENT_GENERATION);
  
  // Verificar formato
  const promptIdPattern = /^pmpt_[a-f0-9]{48}$/;
  const isValidFormat = promptIdPattern.test(config.OPENAI_PROMPT_ID_CONTENT_GENERATION);
  console.log('📝 Formato válido:', isValidFormat ? '✅ Sim' : '❌ Não');
}

console.log('\n🎯 Diagnóstico:');
if (config.OPENAI_API_KEY && config.OPENAI_PROMPT_ID_CONTENT_GENERATION) {
  console.log('✅ Sistema deve usar Prompt ID da OpenAI');
} else if (config.OPENAI_API_KEY) {
  console.log('⚠️ Sistema usará Chat Completions com prompts fallback');
} else {
  console.log('❌ Sistema usará modo mock ou fallback inteligente');
}
