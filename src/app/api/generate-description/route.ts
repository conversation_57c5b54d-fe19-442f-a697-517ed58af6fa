import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import {
  OPENAI_CONFIG,
  type ProductFormData
} from '@/lib/prompts';
import {
  getContentGenerationPrompt,
  getContentImprovementPrompt,
  getOpenAIClientConfig,
  hasPromptIds,
  hasPromptId,
  FALLBACK_PROMPTS,
  replaceFallbackPromptVariables,
  sanitizePromptVariables
} from '@/lib/securePrompts';
import { getSecureConfig } from '@/lib/secureConfig';
import { logSecurityValidation } from '@/lib/securityValidator';
import {
  validateSeoLimits,
  validateApiOutput,
  type SeoContent
} from '@/lib/seoGenerator';
import {
  generateIntelligentFallback,
  generateImprovementFallback
} from '@/lib/fallbackGenerator';
import {
  openaiErrorHandler,
  classifyOpenAIError,
  generateUserFriendlyErrorMessage,
  logError
} from '@/lib/errorHandler';
import { seoContentCache } from '@/lib/cacheSystem';
import { rateLimitMonitor, monitoredApiCall } from '@/lib/rateLimitMonitor';
import { prepareDataForAPI } from '@/lib/inputSanitizer';

// Initialize OpenAI client using secure configuration
let openai: OpenAI | null = null;
let config: ReturnType<typeof getSecureConfig> | null = null;

try {
  // Validate security configuration on startup
  logSecurityValidation();

  config = getSecureConfig();

  if (!config.app.mockMode) {
    const clientConfig = getOpenAIClientConfig();
    openai = new OpenAI(clientConfig);

    // Try fallback key if primary fails
    if (!openai && config.openai.fallbackApiKey) {
      openai = new OpenAI({
        apiKey: config.openai.fallbackApiKey,
        organization: config.openai.organizationId
      });
    }

    console.log('✅ Secure OpenAI client initialized successfully');
  } else {
    console.log('🧪 Running in mock mode - no API calls will be made');
  }
} catch (error) {
  console.error('🚨 Failed to initialize secure OpenAI configuration:', error);
  // Will fall back to mock mode
}

// Manter compatibilidade com interface existente
interface ProductInfo extends ProductFormData {}





// Enhanced mock function using intelligent fallback system
function generateMockSeoContent(productInfo: ProductInfo): SeoContent {
  // Use the intelligent fallback system for consistent mock content
  return generateIntelligentFallback(productInfo);
}













function generateMockImprovedContent(currentDescription: string, productName?: string): SeoContent {
  // Use the intelligent fallback system for consistent mock improvement content
  return generateImprovementFallback(currentDescription, productName);
}

export async function POST(request: Request) {
  // Check if we should use mock mode
  const isInMockMode = config?.app.mockMode || process.env.MOCK_OPENAI === 'true';

  if (isInMockMode) {
    try {
      const body = await request.json();
      let seoContent: SeoContent;

      if (body.action === 'generate') {
        seoContent = generateMockSeoContent(body.productInfo);
      } else if (body.action === 'improve') {
        seoContent = generateMockImprovedContent(body.currentDescription, body.productInfo?.name);
      } else {
        return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
      }

      // Simulate API delay for realistic testing
      await new Promise(resolve => setTimeout(resolve, 1000));

      return NextResponse.json({ seoContent });
    } catch (error) {
      console.error('Erro no modo mock:', error);
      return NextResponse.json({ error: 'Erro no modo de teste.' }, { status: 500 });
    }
  }

  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY ou ative o modo mock.' },
      { status: 500 }
    );
  }

  let body: any;
  try {
    body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    // Log estruturado do erro principal
    logError(error, 'API de descrição de produto', { action: body?.action || 'unknown' });

    // Classificar erro e gerar resposta apropriada
    const errorClassification = classifyOpenAIError(error);
    const userMessage = generateUserFriendlyErrorMessage(error);

    console.error(`❌ Erro na API de descrição de produto (${errorClassification.category}):`, errorClassification.technicalMessage);

    // Determinar status HTTP baseado na classificação
    let httpStatus = 500;
    switch (errorClassification.category) {
      case 'auth':
        httpStatus = 401;
        break;
      case 'quota':
        httpStatus = 429;
        break;
      case 'client':
        httpStatus = 400;
        break;
      case 'server':
      case 'network':
        httpStatus = 503;
        break;
      default:
        httpStatus = 500;
    }

    return NextResponse.json({
      error: userMessage,
      category: errorClassification.category,
      severity: errorClassification.severity,
      canRetry: errorClassification.shouldRetry
    }, { status: httpStatus });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  // Preparar e sanitizar dados do produto
  const preparation = prepareDataForAPI(productInfo);
  if (!preparation.isValid) {
    throw new Error(`Dados inválidos: ${preparation.errors.join(', ')}`);
  }

  // Log warnings se existirem
  if (preparation.warnings.length > 0) {
    console.warn('⚠️ Avisos de sanitização:', preparation.warnings);
  }

  // Usar dados sanitizados
  const sanitizedProductInfo = preparation.data;

  // Verificar cache primeiro (usando dados sanitizados)
  const cachedContent = seoContentCache.get(sanitizedProductInfo, 'generate');
  if (cachedContent) {
    console.log(`🚀 Conteúdo recuperado do cache para: ${sanitizedProductInfo.name}`);
    return cachedContent;
  }

  // Usar sistema de prompts seguro
  let response: any;

  try {
    if (hasPromptIds()) {
      // Usar prompt IDs seguros - Implementação híbrida
      const promptConfig = getContentGenerationPrompt(sanitizedProductInfo);

      console.log('🔒 Using secure prompt ID:', promptConfig.promptId);
      console.log('📝 Prompt variables:', sanitizePromptVariables(promptConfig.variables || {}));
      console.log('🔍 Product data being processed:', {
        name: sanitizedProductInfo.name,
        category: sanitizedProductInfo.category,
        features: sanitizedProductInfo.features,
        targetAudience: sanitizedProductInfo.targetAudience
      });

      // Verificar se a Responses API está disponível
      if (typeof (openai as any).responses?.create === 'function') {
        // Use the new Responses API with Prompt IDs
        console.log('🚀 Using Responses API with Prompt ID');
        response = await openaiErrorHandler.executeWithRetry(
          () => monitoredApiCall(
            () => (openai as any).responses.create({
              model: null, // Model is inherited from the prompt
              prompt: {
                id: promptConfig.promptId,
                variables: promptConfig.variables || {}
              },
              input: [] // Empty input as required by Responses API
            }),
            preparation.estimatedTokens
          ),
          'Geração de conteúdo SEO (Responses API)'
        );
      } else {
        // Fallback para Chat Completions com variáveis injetadas
        console.log('⚠️ Responses API not available, using Chat Completions with variable injection');
        const userPrompt = replaceFallbackPromptVariables(
          FALLBACK_PROMPTS.contentGeneration,
          promptConfig.variables || {}
        );

        response = await openaiErrorHandler.executeWithRetry(
          () => monitoredApiCall(
            () => openai.chat.completions.create({
              ...OPENAI_CONFIG,
              messages: [
                {
                  role: "user",
                  content: userPrompt
                }
              ]
            }),
            preparation.estimatedTokens
          ),
          'Geração de conteúdo SEO (Chat Completions Fallback)'
        );
      }
    } else {
      // Fallback para prompts tradicionais
      console.log('⚠️ Using fallback prompts (prompt IDs not available)');

      const promptConfig = getContentGenerationPrompt(sanitizedProductInfo);
      const userPrompt = replaceFallbackPromptVariables(
        FALLBACK_PROMPTS.contentGeneration,
        promptConfig.variables || {}
      );

      response = await openaiErrorHandler.executeWithRetry(
        () => monitoredApiCall(
          () => openai.chat.completions.create({
            ...OPENAI_CONFIG,
            messages: [
              {
                role: "user",
                content: userPrompt
              }
            ]
          }),
          preparation.estimatedTokens
        ),
        'Geração de conteúdo SEO (Fallback)'
      );
    }

    // Atualizar informações de rate limit a partir dos headers
    if (response && typeof response === 'object' && 'headers' in response) {
      rateLimitMonitor.updateFromHeaders(response.headers as Headers);
    }

    // Extrair conteúdo baseado no tipo de resposta (Responses API vs Chat Completions)
    let content: string;
    if ('output_text' in response) {
      // Responses API format
      content = response.output_text;
      console.log('📝 Content extracted from Responses API');
    } else if ('choices' in response && response.choices?.[0]?.message?.content) {
      // Chat Completions format
      content = response.choices[0].message.content;
      console.log('📝 Content extracted from Chat Completions API');
    } else {
      throw new Error("A resposta da API está vazia ou em formato inesperado.");
    }

    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const rawContent = JSON.parse(content);

      // Usar validação robusta do output da API com otimização automática
      const apiValidation = validateApiOutput(rawContent);

      if (!apiValidation.isValid) {
        console.error("Output da API inválido:", apiValidation.errors);
        throw new Error(`Resposta da API inválida: ${apiValidation.errors.join(', ')}`);
      }

      const parsedContent = apiValidation.correctedContent!;

      // Log SEO optimizations applied during validation
      if (apiValidation.seoWarnings && apiValidation.seoWarnings.length > 0) {
        console.log("✅ Otimizações SEO aplicadas automaticamente:", apiValidation.seoWarnings);
      }

      // Additional SEO validation for monitoring (non-blocking)
      const seoValidation = validateSeoLimits(parsedContent);
      if (seoValidation.warnings.length > 0) {
        console.warn("⚠️ Avisos de SEO adicionais:", seoValidation.warnings);
      }

      console.log(`✅ Conteúdo SEO gerado com sucesso para: ${sanitizedProductInfo.name}`);
      console.log(`📊 Meta description: ${parsedContent.shortDescription.length} caracteres`);
      console.log(`📊 Descrição principal: ${parsedContent.wooCommerceMainDescription.length} caracteres`);

      // Armazenar no cache para futuras consultas (usando dados sanitizados)
      seoContentCache.set(sanitizedProductInfo, parsedContent, 'generate');

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Log estruturado do erro
    console.error('🚨 ERRO DETALHADO na geração de conteúdo:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      productName: sanitizedProductInfo.name,
      hasPromptIds: hasPromptIds(),
      promptId: hasPromptIds() ? getContentGenerationPrompt(sanitizedProductInfo).promptId : 'N/A'
    });

    logError(error, 'Geração de conteúdo SEO', { productName: sanitizedProductInfo.name });

    // Classificar erro para determinar estratégia
    const errorClassification = classifyOpenAIError(error);

    console.warn(
      `⚠️ API OpenAI falhou (${errorClassification.category}): ${errorClassification.technicalMessage}. ` +
      'Usando fallback inteligente.'
    );

    // Usar fallback inteligente quando a API falha (com dados sanitizados)
    const fallbackContent = generateIntelligentFallback(sanitizedProductInfo);

    // Validar o conteúdo do fallback
    const validation = validateSeoLimits(fallbackContent);
    if (!validation.isValid) {
      console.warn("Fallback não atende limites SEO:", validation.errors);
    }

    return fallbackContent;
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  // Validar se a descrição atual não está vazia
  if (!currentDescription || currentDescription.trim().length === 0) {
    throw new Error('Descrição atual é obrigatória para melhoria');
  }

  // Criar dados fictícios do produto para cache (baseado na descrição)
  const productDataForCache = {
    name: productName || 'Produto para Melhoria',
    category: '',
    features: [],
    keywords: [],
    targetAudience: '',
    additionalInfo: currentDescription.substring(0, 100) // Usar parte da descrição como identificador
  };

  // Verificar cache primeiro
  const cachedContent = seoContentCache.get(productDataForCache, 'improve');
  if (cachedContent) {
    console.log(`🚀 Conteúdo de melhoria recuperado do cache para: ${productName || 'produto'}`);
    return cachedContent;
  }

  // Usar sistema de prompts seguro para melhoria
  let response: any;

  try {
    if (hasPromptId('contentImprovement')) {
      // Usar prompt IDs seguros para melhoria
      const promptConfig = getContentImprovementPrompt(currentDescription, productName);

      console.log('🔒 Using secure improvement prompt ID:', promptConfig.promptId);
      console.log('📝 Improvement variables:', sanitizePromptVariables(promptConfig.variables || {}));

      // Verificar se a Responses API está disponível
      if (typeof (openai as any).responses?.create === 'function') {
        // Use the new Responses API with Prompt IDs
        console.log('🚀 Using Responses API for improvement');
        response = await openaiErrorHandler.executeWithRetry(
          () => (openai as any).responses.create({
            model: null, // Model is inherited from the prompt
            prompt: {
              id: promptConfig.promptId,
              variables: promptConfig.variables || {}
            },
            input: [] // Empty input as required by Responses API
          }),
          'Melhoria de conteúdo SEO (Responses API)'
        );
      } else {
        // Fallback para Chat Completions com variáveis injetadas
        console.log('⚠️ Responses API not available for improvement, using Chat Completions');
        const userPrompt = replaceFallbackPromptVariables(
          FALLBACK_PROMPTS.contentImprovement,
          promptConfig.variables || {}
        );

        response = await openaiErrorHandler.executeWithRetry(
          () => openai.chat.completions.create({
            ...OPENAI_CONFIG,
            messages: [
              {
                role: "user",
                content: userPrompt
              }
            ]
          }),
          'Melhoria de conteúdo SEO (Chat Completions Fallback)'
        );
      }
    } else {
      // Fallback para prompts tradicionais
      console.log('⚠️ Using fallback improvement prompts');

      const promptConfig = getContentImprovementPrompt(currentDescription, productName);
      const userPrompt = replaceFallbackPromptVariables(
        FALLBACK_PROMPTS.contentImprovement,
        promptConfig.variables || {}
      );

      response = await openaiErrorHandler.executeWithRetry(
        () => openai.chat.completions.create({
          ...OPENAI_CONFIG,
          messages: [
            {
              role: "user",
              content: userPrompt
            }
          ]
        }),
        'Melhoria de conteúdo SEO (Fallback)'
      );
    }

    // Extrair conteúdo baseado no tipo de resposta (Responses API vs Chat Completions)
    let content: string;
    if ('output_text' in response) {
      // Responses API format
      content = response.output_text;
      console.log('📝 Improvement content extracted from Responses API');
    } else if ('choices' in response && response.choices?.[0]?.message?.content) {
      // Chat Completions format
      content = response.choices[0].message.content;
      console.log('📝 Improvement content extracted from Chat Completions API');
    } else {
      throw new Error("A resposta da API está vazia ou em formato inesperado.");
    }

    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const rawContent = JSON.parse(content);

      // Usar validação robusta do output da API
      const apiValidation = validateApiOutput(rawContent);

      if (!apiValidation.isValid) {
        console.error("Output da API inválido:", apiValidation.errors);
        throw new Error(`Resposta da API inválida: ${apiValidation.errors.join(', ')}`);
      }

      const parsedContent = apiValidation.correctedContent!;

      // Log SEO optimizations applied during validation
      if (apiValidation.seoWarnings && apiValidation.seoWarnings.length > 0) {
        console.log("✅ Otimizações SEO aplicadas automaticamente na melhoria:", apiValidation.seoWarnings);
      }

      // Additional SEO validation for monitoring (non-blocking)
      const seoValidation = validateSeoLimits(parsedContent);
      if (seoValidation.warnings.length > 0) {
        console.warn("⚠️ Avisos de SEO adicionais na melhoria:", seoValidation.warnings);
      }

      console.log(`✅ Conteúdo melhorado com sucesso`);
      console.log(`📊 Meta description: ${parsedContent.shortDescription.length} caracteres`);

      // Armazenar no cache para futuras consultas
      seoContentCache.set(productDataForCache, parsedContent, 'improve');

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Log estruturado do erro
    logError(error, 'Melhoria de conteúdo SEO', { productName });

    // Classificar erro para determinar estratégia
    const errorClassification = classifyOpenAIError(error);

    console.warn(
      `⚠️ API OpenAI falhou na melhoria (${errorClassification.category}): ${errorClassification.technicalMessage}. ` +
      'Usando fallback inteligente.'
    );

    // Usar fallback inteligente para melhoria quando a API falha
    const fallbackContent = generateImprovementFallback(currentDescription, productName);

    // Validar o conteúdo do fallback
    const validation = validateSeoLimits(fallbackContent);
    if (!validation.isValid) {
      console.warn("Fallback de melhoria não atende limites SEO:", validation.errors);
    }

    return fallbackContent;
  }
}
