'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Trash2, Clipboard, FileText, Loader, Plus, Sparkles, ImageIcon } from 'lucide-react';
import '../styles/product-description.css';

// Tipos de dados
interface ProductInfo {
  name: string;
  category: string;
  features: string[];
  keywords: string[];
  targetAudience: string;
  additionalInfo: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
}

// Componente para exibir um campo de resultado
const ResultField = ({ title, content, onCopy }: { title: string; content: string; onCopy: (text: string, field: string) => void; }) => {
  const charCount = content.length;
  let textColorClass = "text-gray-700 dark:text-gray-300";
  let statusMessage = "";

  if (title === "Descrição SEO") {
    if (charCount >= 140 && charCount <= 160) {
      textColorClass = "text-green-600 dark:text-green-400";
      statusMessage = "✓ Ideal para SEO";
    } else if (charCount >= 120 && charCount < 140) {
      textColorClass = "text-orange-600 dark:text-orange-400";
      statusMessage = "⚠ Muito curta para SEO";
    } else if (charCount > 160 && charCount <= 170) {
      textColorClass = "text-orange-600 dark:text-orange-400";
      statusMessage = "⚠ Ligeiramente longa";
    } else {
      textColorClass = "text-red-600 dark:text-red-400";
      statusMessage = charCount < 120 ? "✗ Muito curta" : "✗ Muito longa para SEO";
    }
  }

  // Verificar se o conteúdo contém HTML (para descrições do WooCommerce)
  const isHtmlContent = title.includes("WooCommerce") && content.includes('<');

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h4 className="text-md font-semibold text-gray-800 dark:text-gray-200">{title}</h4>
        <div className="flex items-center gap-2">
          {(title === "Descrição SEO") && (
            <div className="flex items-center gap-2">
              <span className={`text-xs font-medium ${textColorClass}`}>
                {charCount}/160 caracteres
              </span>
              <span className={`text-xs ${textColorClass}`}>
                {statusMessage}
              </span>
            </div>
          )}
          <Button
            onClick={() => onCopy(content, title)}
            variant="outline"
            size="sm"
            className="flex items-center gap-1.5 text-xs h-8 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
          >
            <Clipboard className="h-3.5 w-3.5" />
            <span>Copiar</span>
          </Button>
        </div>
      </div>
      <div className={`max-w-none p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700 text-sm font-sans ${isHtmlContent ? 'formatted-content-container' : 'whitespace-pre-wrap'}`}>
        {isHtmlContent ? (
          <div
            dangerouslySetInnerHTML={{ __html: content }}
            className="formatted-content"
          />
        ) : (
          <div className="simple-content">
            {content}
          </div>
        )}
      </div>

    </div>
  );
};

const ProductDescriptionGenerator = () => {
  // Estados
  const [productInfo, setProductInfo] = useState<ProductInfo>({
    name: '',
    category: '',
    features: [''],
    keywords: [''],
    targetAudience: '',
    additionalInfo: ''
  });

  const [generatedContent, setGeneratedContent] = useState<SeoContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const descriptionSectionRef = useRef<HTMLDivElement>(null);

  // Efeitos para LocalStorage
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('productDescriptionFormData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setProductInfo(parsedData.productInfo || { name: '', category: '', features: [''], keywords: [''], targetAudience: '', additionalInfo: '' });
      }
    } catch (error) {
      console.error("Failed to load data from localStorage", error);
    }
  }, []);

  useEffect(() => {
    try {
      const dataToSave = JSON.stringify({ productInfo });
      localStorage.setItem('productDescriptionFormData', dataToSave);
    } catch (error) {
      console.error("Failed to save data to localStorage", error);
    }
  }, [productInfo]);

  // Manipulador de API para geração
  const handleGenerate = async () => {
    if (!productInfo.name) {
      toast.error('O nome do produto é obrigatório.');
      return;
    }

    setIsLoading(true);
    const toastId = toast.loading('A gerar conteúdo SEO...');

    try {
      // Auto-generate keywords if none provided
      let keywords = productInfo.keywords.filter(k => k.trim() !== '');
      if (keywords.length === 0) {
        // Generate basic keywords from product name and category
        const nameWords = productInfo.name.toLowerCase().split(' ').filter(w => w.length > 2);
        const categoryWords = productInfo.category.toLowerCase().split(' ').filter(w => w.length > 2);
        keywords = [...nameWords, ...categoryWords].slice(0, 3);
      }

      const response = await fetch('/api/generate-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productInfo: {
            ...productInfo,
            features: productInfo.features.filter(f => f.trim() !== ''),
            keywords: keywords
          }
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Ocorreu um erro desconhecido.');

      setGeneratedContent(data.seoContent);
      toast.success('Conteúdo SEO gerado com sucesso!', { id: toastId });

      setTimeout(() => {
        descriptionSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

    } catch (error: unknown) {
      console.error('Erro ao gerar descrição:', error);

      let errorMessage = 'Erro desconhecido';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide more user-friendly messages for common errors
        if (errorMessage.includes('quota')) {
          errorMessage = 'Quota da API excedida. Por favor, tente novamente mais tarde ou contacte o suporte.';
        } else if (errorMessage.includes('401') || errorMessage.includes('inválida')) {
          errorMessage = 'Problema de autenticação com a API. Por favor, contacte o suporte.';
        } else if (errorMessage.includes('503') || errorMessage.includes('indisponível')) {
          errorMessage = 'Serviço temporariamente indisponível. Tente novamente em alguns minutos.';
        } else if (errorMessage.includes('400') || errorMessage.includes('inválido')) {
          errorMessage = 'Dados inválidos. Verifique as informações do produto e tente novamente.';
        } else if (errorMessage.includes('JSON')) {
          errorMessage = 'Resposta inválida do servidor. Tente novamente.';
        }
      }

      toast.error(`Falha ao gerar conteúdo. ${errorMessage}`, { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };



  // Simple function to convert HTML to clean text with proper paragraph formatting
  const stripHtmlTags = (html: string): string => {
    if (!html) return '';

    // Apply automatic error correction first
    let correctedHtml = correctTextErrors(html);

    // Process HTML tags to preserve paragraph structure
    let processedHtml = correctedHtml
      .replace(/<h[1-6][^>]*>/gi, '') // Remove heading opening tags
      .replace(/<\/h[1-6]>/gi, '\n\n') // Headings with double line break
      .replace(/<p[^>]*class="intro-paragraph"[^>]*>/gi, '') // Remove intro paragraph tags
      .replace(/<p[^>]*class="technical-paragraph"[^>]*>/gi, '\n\n') // Technical paragraph with line break
      .replace(/<p[^>]*class="usage-paragraph"[^>]*>/gi, '\n\n') // Usage paragraph with line break
      .replace(/<p[^>]*class="closing-paragraph"[^>]*>/gi, '\n\n') // Closing paragraph with line break
      .replace(/<\/p>/gi, '\n\n') // All paragraph endings with double line break
      .replace(/<p[^>]*>/gi, '') // Remove remaining paragraph opening tags
      .replace(/<br\s*\/?>/gi, '\n') // Line breaks
      .replace(/<[^>]*>/g, ''); // Remove all remaining HTML tags

    // Create temporary element to decode HTML entities
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedHtml;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Clean up spacing and formatting while preserving paragraph structure
    return textContent
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Maximum two consecutive line breaks
      .replace(/^\s+|\s+$/g, '') // Remove spaces at beginning and end
      .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space (but preserve line breaks)
      .replace(/\n /g, '\n') // Remove spaces after line breaks
      .replace(/\n\n\n+/g, '\n\n') // Ensure maximum two line breaks
      .trim();
  };

  // Function to correct common text errors
  const correctTextErrors = (text: string): string => {
    if (!text) return '';

    let corrected = text;

    // Fix spacing issues
    corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
    corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
    corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

    // Fix capitalization after punctuation
    corrected = corrected.replace(/([.!?])\s+([a-z])/g, (_, punct, letter) => {
      return punct + ' ' + letter.toUpperCase();
    });

    // Common Portuguese spelling corrections
    const corrections: { [key: string]: string } = {
      'qualidadde': 'qualidade',
      'funcionalidadde': 'funcionalidade',
      'resistênte': 'resistente',
      'duravél': 'durável',
      'electrónico': 'eletrónico',
      'electrónicos': 'eletrónicos',
      'electrónica': 'eletrónica',
      'optimizar': 'otimizar',
      'optimizado': 'otimizado'
    };

    Object.entries(corrections).forEach(([wrong, right]) => {
      const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
      corrected = corrected.replace(regex, right);
    });

    return corrected;
  };

  // Enhanced UI handlers with automatic error correction
  const handleCopy = (text: string, fieldName: string) => {
    if (!text) return;

    // Check if content contains HTML (for WooCommerce descriptions)
    const isHtmlContent = fieldName.includes("WooCommerce") && text.includes('<');

    // Apply error correction and convert to appropriate format
    let textToCopy = text;

    if (isHtmlContent) {
      // Convert HTML to clean text that matches display exactly
      textToCopy = stripHtmlTags(text);
    } else {
      // Apply error correction to plain text
      textToCopy = correctTextErrors(text);
    }

    // Ensure consistent formatting for clipboard
    textToCopy = textToCopy
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Max two line breaks
      .trim();

    navigator.clipboard.writeText(textToCopy)
      .then(() => toast.success(`${fieldName} copiado para a área de transferência!`))
      .catch(() => toast.error('Falha ao copiar.'));
  };

  const handleReset = () => {
    try {
      localStorage.removeItem('productDescriptionFormData');
    } catch (error) {
      console.error("Failed to clear localStorage", error);
    }
    setGeneratedContent(null);
    setProductInfo({ name: '', category: '', features: [''], keywords: [''], targetAudience: '', additionalInfo: '' });
    toast.success('Formulário limpo e pronto para um novo produto!');
  };

  // Manipuladores de formulário
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProductInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleArrayInputChange = (index: number, value: string, field: 'features' | 'keywords') => {
    const newArray = [...productInfo[field]];
    newArray[index] = value;
    setProductInfo(prev => ({ ...prev, [field]: newArray }));
  };

  const addArrayItem = (field: 'features' | 'keywords') => {
    setProductInfo(prev => ({ ...prev, [field]: [...prev[field], ''] }));
  };

  const removeArrayItem = (index: number, field: 'features' | 'keywords') => {
    if (productInfo[field].length <= 1) return;
    const newArray = productInfo[field].filter((_, i) => i !== index);
    setProductInfo(prev => ({ ...prev, [field]: newArray }));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
      {/* Coluna do Formulário */}
      <div className="lg:col-span-5 space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="space-y-6">
            <div className="flex flex-col gap-3 mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Gerador de Conteúdo SEO
              </h3>
            </div>

            {/* Formulário de Geração */}
            <div className="space-y-8">
                {/* Campo Principal - Nome do Produto */}
                <div className="space-y-3">
                    <label htmlFor="name" className="block text-sm font-semibold text-gray-900 dark:text-white">
                        Nome do Produto <span className="text-red-500">*</span>
                    </label>
                    <Input
                        id="name"
                        name="name"
                        value={productInfo.name}
                        onChange={handleInputChange}
                        placeholder="Ex: Sapatilhas de Corrida Leves"
                        className="h-12 text-base border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                    />
                </div>

                {/* Campos Opcionais em Layout Compacto */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                        <label htmlFor="category" className="block text-sm font-semibold text-gray-900 dark:text-white">
                            Categoria
                        </label>
                        <Input
                            id="category"
                            name="category"
                            value={productInfo.category}
                            onChange={handleInputChange}
                            placeholder="Ex: Calçado Desportivo"
                            className="h-12 text-base border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                        />
                    </div>
                    <div className="space-y-3">
                        <label htmlFor="targetAudience" className="block text-sm font-semibold text-gray-900 dark:text-white">
                            Público-alvo
                        </label>
                        <Input
                            id="targetAudience"
                            name="targetAudience"
                            value={productInfo.targetAudience}
                            onChange={handleInputChange}
                            placeholder="Ex: Corredores amadores"
                            className="h-12 text-base border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                        />
                    </div>
                </div>

                {/* Características Principais - Simplificado */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <label className="block text-sm font-semibold text-gray-900 dark:text-white">
                            Características Principais
                        </label>
                        <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">Opcional - máx. 3</span>
                    </div>
                    <div className="space-y-3">
                        {productInfo.features.slice(0, 3).map((feature, index) => (
                            <div key={`feature-${index}`} className="flex gap-3">
                                <Input
                                    value={feature}
                                    onChange={(e) => handleArrayInputChange(index, e.target.value, 'features')}
                                    placeholder={`Característica ${index + 1}`}
                                    className="h-11 text-base border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                                />
                                {productInfo.features.length > 1 && (
                                    <Button
                                        type="button"
                                        variant="destructive"
                                        size="icon"
                                        onClick={() => removeArrayItem(index, 'features')}
                                        className="h-11 w-11"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                )}
                            </div>
                        ))}
                        {productInfo.features.length < 3 && (
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => addArrayItem('features')}
                                className="w-full h-10 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                Adicionar Característica
                            </Button>
                        )}
                    </div>
                </div>

                {/* Informações Adicionais */}
                <div className="space-y-3">
                    <label htmlFor="additionalInfo" className="block text-sm font-semibold text-gray-900 dark:text-white">
                        Informações Adicionais
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 font-normal">Opcional</span>
                    </label>
                    <Textarea
                        id="additionalInfo"
                        name="additionalInfo"
                        value={productInfo.additionalInfo}
                        onChange={handleInputChange}
                        placeholder="Detalhes sobre material, tecnologia, benefícios específicos..."
                        rows={4}
                        className="text-base border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 transition-colors resize-none"
                    />
                </div>

                {/* Botão de Geração */}
                <Button
                    onClick={handleGenerate}
                    disabled={isLoading || !productInfo.name}
                    className="w-full h-12 mt-6 text-base font-medium"
                >
                    {isLoading ? (
                        <>
                            <Loader className="mr-2 h-5 w-5 animate-spin" />
                            A gerar conteúdo...
                        </>
                    ) : (
                        <>
                            <FileText className="mr-2 h-5 w-5" />
                            Gerar Conteúdo SEO
                        </>
                    )}
                </Button>

                {/* Palavras-chave Auto-geradas (Ocultas do utilizador) */}
                <div className="hidden">
                    {productInfo.keywords.map((keyword, index) => (
                        <input
                            key={`hidden-keyword-${index}`}
                            type="hidden"
                            value={keyword}
                        />
                    ))}
                </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coluna de Resultados */}
      <div ref={descriptionSectionRef} className="lg:col-span-7">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm sticky top-6">
          <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Conteúdo Gerado</h3>
            <Button onClick={handleReset} variant="destructive" size="sm" className="flex items-center gap-1.5 h-9 text-xs">
              <Trash2 className="h-3.5 w-3.5" />
              <span>Limpar Tudo</span>
            </Button>
          </div>
          
          <div className="p-6 space-y-6 max-h-[80vh] overflow-y-auto">
            {generatedContent ? (
              <>
                <ResultField title="Descrição WooCommerce" content={generatedContent.wooCommerceMainDescription} onCopy={handleCopy} />
                <ResultField title="Curta Descrição WooCommerce" content={generatedContent.wooCommerceShortDescription} onCopy={handleCopy} />
                <ResultField title="Descrição SEO" content={generatedContent.shortDescription} onCopy={handleCopy} />
                <ResultField title="Slug" content={generatedContent.slug} onCopy={handleCopy} />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center text-center p-10 h-full min-h-[400px]">
                <div className="w-16 h-16 mb-6 rounded-full bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-3">A aguardar informações</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                  Preencha os dados do produto para gerar uma descrição principal, uma meta description para SEO e um slug de URL otimizado.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDescriptionGenerator;
