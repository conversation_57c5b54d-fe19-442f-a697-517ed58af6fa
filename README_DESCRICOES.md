# Sistema de Geração de Descrições de Produtos

## 📋 Visão Geral

Este sistema utiliza a API da OpenAI (GPT-4o) para gerar descrições de produtos otimizadas para e-commerce, com foco em SEO e WooCommerce. O sistema implementa práticas de segurança avançadas e fallbacks inteligentes.

## 🏗️ Arquitetura do Sistema

### Componentes Principais

1. **Frontend**: `ProductDescriptionGenerator.tsx`
2. **API Backend**: `/api/generate-description/route.ts`
3. **Configuração Segura**: `secureConfig.ts` e `securePrompts.ts`
4. **Validação e Fallbacks**: Sistema robusto de tratamento de erros

## 🔧 Como Funciona

### 1. Fluxo de Geração de Descrições

```mermaid
graph TD
    A[Utilizador preenche formulário] --> B[Frontend valida dados]
    B --> C[Chamada para /api/generate-description]
    C --> D{API Key configurada?}
    D -->|Sim| E[Preparação e sanitização dos dados]
    D -->|Não| F[Modo Mock ativado]
    E --> G{Prompt IDs disponíveis?}
    G -->|Sim| H[Usa Prompt IDs seguros]
    G -->|Não| I[Usa prompts fallback]
    H --> J[Chamada OpenAI API]
    I --> J
    J --> K{Sucesso?}
    K -->|Sim| L[Valida resposta]
    K -->|Não| M[Fallback inteligente]
    L --> N[Retorna conteúdo SEO]
    M --> N
    F --> O[Gera conteúdo mock]
    O --> N
```

### 2. Estrutura dos Dados

#### Input (ProductInfo)
```typescript
interface ProductInfo {
  name: string;           // Nome do produto (obrigatório)
  category: string;       // Categoria do produto
  features: string[];     // Características/funcionalidades
  keywords: string[];     // Palavras-chave SEO
  targetAudience: string; // Público-alvo
  additionalInfo: string; // Informações adicionais
}
```

#### Output (SeoContent)
```typescript
interface SeoContent {
  shortDescription: string;           // Meta description SEO (140-160 chars)
  slug: string;                      // URL amigável
  wooCommerceMainDescription: string; // Descrição principal HTML
  wooCommerceShortDescription: string; // Resumo (30 palavras)
}
```

## 🔐 Configuração da API

### Variáveis de Ambiente Obrigatórias

```bash
# API OpenAI
OPENAI_API_KEY=sk-proj-your-api-key-here
OPENAI_FALLBACK_API_KEY=sk-proj-your-fallback-key-here
OPENAI_ORGANIZATION_ID=org-your-org-id-here

# Prompt IDs Seguros (Sistema de Prompts da OpenAI)
OPENAI_PROMPT_ID_CONTENT_GENERATION=pmpt_6872bf9517b08196b785b37c76b7821f0a7790a11376660a
OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT=pmpt_placeholder_improvement
OPENAI_PROMPT_ID_KEYWORD_SUGGESTION=pmpt_placeholder_keywords

# Configurações de Segurança
DISABLE_RATE_LIMITING=false
MAX_REQUESTS_PER_MINUTE=60
DISABLE_INPUT_SANITIZATION=false

# Modo de Desenvolvimento
MOCK_OPENAI=false  # true para modo mock (sem consumir API)
NODE_ENV=development
LOG_LEVEL=info
```

### Configuração do Modelo OpenAI

```typescript
export const OPENAI_CONFIG = {
  model: "gpt-4o",                    // Modelo GPT-4o
  temperature: 0.3,                   // Consistência alta
  max_completion_tokens: 800,         // Limite otimizado
  response_format: { type: "json_object" },
  top_p: 0.9,
  frequency_penalty: 0.2,             // Reduz repetições
  presence_penalty: 0.1               // Encoraja diversidade
};
```

## 🛡️ Sistema de Segurança

### 1. Gestão Segura de Credenciais
- **Validação server-side**: Credenciais nunca expostas no cliente
- **Fallback keys**: Sistema de chaves de backup
- **Validação de formato**: Verificação automática de API keys

### 2. Sistema de Prompt IDs
- **Prompts seguros**: Utiliza sistema de Prompt IDs da OpenAI
- **Fallback prompts**: Sistema de backup para prompts hardcoded
- **Sanitização**: Limpeza automática de variáveis de entrada

### 3. Rate Limiting e Monitorização
- **Controlo de taxa**: Limite configurável de requests por minuto
- **Monitorização**: Tracking de uso e performance
- **Retry logic**: Sistema inteligente de tentativas

## 🔄 Sistema de Fallbacks

### 1. Fallback Inteligente
Quando a API falha, o sistema gera conteúdo usando templates inteligentes:

```typescript
// Exemplo de fallback para descrição principal
const fallbackContent = {
  wooCommerceMainDescription: `
    <p><strong>${productName}</strong> - a solução ideal para ${category}.</p>
    <p><strong>Características principais:</strong></p>
    <ul>${features.map(f => `<li>${f}</li>`).join('')}</ul>
    <p>Produto de qualidade superior, desenvolvido para ${targetAudience}.</p>
  `,
  // ... outros campos
};
```

### 2. Modo Mock
Para desenvolvimento sem consumir API:

```bash
MOCK_OPENAI=true
```

Gera conteúdo realista para testes sem custos.

## 📊 Validação e Qualidade

### 1. Validação SEO
- **Meta descriptions**: 140-160 caracteres obrigatórios
- **Slugs**: Formato URL-friendly automático
- **Keywords**: Geração automática se não fornecidas

### 2. Validação de Conteúdo
- **Sanitização**: Limpeza automática de inputs
- **Estrutura HTML**: Validação de formato
- **Limites de caracteres**: Verificação automática

### 3. Correção Automática
- **Gramática portuguesa**: Correção automática
- **Concordância de género**: Validação de artigos (o/a)
- **Estrutura profissional**: Transformação de texto informal

## 🚀 Como Usar

### 1. Configuração Inicial
```bash
# 1. Copiar variáveis de ambiente
cp .env.example .env.local

# 2. Configurar API key da OpenAI
# Editar .env.local com suas credenciais

# 3. Instalar dependências
npm install

# 4. Executar em desenvolvimento
npm run dev
```

### 2. Utilização no Frontend
```typescript
// Exemplo de chamada
const response = await fetch('/api/generate-description', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'generate',
    productInfo: {
      name: 'Smartphone XYZ',
      category: 'Eletrónica',
      features: ['5G', 'Câmara 48MP', 'Bateria 5000mAh'],
      keywords: ['smartphone', 'android', '5g'],
      targetAudience: 'Jovens profissionais',
      additionalInfo: 'Disponível em várias cores'
    }
  })
});

const { seoContent } = await response.json();
```

## 🔍 Monitorização e Logs

### Logs de Segurança
```
🔒 Secure configuration loaded
✅ Secure OpenAI client initialized successfully
🔒 Using secure prompt ID: pmpt_6872bf95...
```

### Logs de Performance
```
📊 Rate limit status: 45/60 requests remaining
⚡ API response time: 1.2s
📈 Token usage: 245 tokens
```

### Logs de Fallback
```
⚠️ API OpenAI falhou (rate_limit): Quota exceeded. Usando fallback inteligente.
🧪 Running in mock mode - no API calls will be made
```

## 🛠️ Resolução de Problemas

### Quota Excedida
1. **Ativar modo mock**: `MOCK_OPENAI=true`
2. **Verificar billing**: Dashboard OpenAI
3. **Usar fallback key**: Configurar `OPENAI_FALLBACK_API_KEY`

### Erros de Configuração
1. **Verificar .env.local**: Todas as variáveis definidas
2. **Validar API key**: Formato `sk-proj-...`
3. **Testar conectividade**: Logs de inicialização

### Performance
1. **Ajustar rate limits**: `MAX_REQUESTS_PER_MINUTE`
2. **Otimizar prompts**: Reduzir tokens
3. **Cache**: Sistema automático implementado

## 📈 Métricas e Analytics

O sistema inclui monitorização automática de:
- **Taxa de sucesso** das chamadas API
- **Tempo de resposta** médio
- **Uso de tokens** por request
- **Rate limiting** effectiveness
- **Fallback usage** frequency

## 🔄 Atualizações e Manutenção

### Rotação de API Keys
1. Gerar nova key no dashboard OpenAI
2. Atualizar `OPENAI_API_KEY`
3. Manter antiga como `OPENAI_FALLBACK_API_KEY`
4. Monitorizar logs para confirmar funcionamento

### Atualização de Prompts
1. Criar novo Prompt ID no dashboard OpenAI
2. Atualizar variável de ambiente correspondente
3. Testar em ambiente de desenvolvimento
4. Deploy para produção

---

**Nota**: Este sistema foi desenvolvido seguindo as melhores práticas de segurança e performance para aplicações de produção com integração OpenAI.
